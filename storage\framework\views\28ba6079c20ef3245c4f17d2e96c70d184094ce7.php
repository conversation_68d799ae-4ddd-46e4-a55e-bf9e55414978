
<?php $__env->startSection('page_content'); ?>
    <div class="page-content-container">
        <section class="hero-section bg-gray d-flex align-items-center justify-content-center padding-medium pb-5">
            <div class="hero-content">
                <div class="container">
                    <div class="row">
                        <div class="text-center padding-medium no-padding-bottom">
                            <h1><span class="highlighted-title">Edit Book</span></h1>


                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="padding-medium">
            <div class="container bg-light p-5">
                <?php if($Author == false): ?>
                    <?php if($isChnagesRequested): ?>
                        <div class="text-danger">
                            <h3>Required Changes: </h3>
                            <p class="text-danger fw-bold"><?php echo e($isChnagesRequested->ModificationText); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if(isset($book->modificationRequired) && $book->modificationRequired->status == true): ?>
                        <div class="text-danger">
                            <h3>Required Changes: </h3>
                            <p class="text-danger fw-bold"><?php echo e($book->modificationRequired->Modification); ?></p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>


                <?php if($Author): ?>
                    <form method="POST" action="<?php echo e(route('admin.book.edit', $book->id)); ?>" enctype="multipart/form-data">
                    <?php else: ?>
                        <form method="POST" action="<?php echo e(route('edit-book.author', $book->id)); ?>"
                            enctype="multipart/form-data">
                <?php endif; ?>
                <?php echo csrf_field(); ?>


                <?php if($errors->any()): ?>
                    <div class="alert alert-danger">
                        <ul>
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>
                <div class="row gy-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="book-title">Book Title</label>

                            <input type="text" readonly value="<?php echo e($book->title); ?>" name="book-title"
                                class="form-control mt-2">

                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="book-author">Book Author</label>
                            <input type="text" readonly value="<?php echo e($book->author); ?>" name="book-author"
                                class="form-control mt-2">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="book-category">Book Category</label>
                            <select name="book-category" class="form-select mt-2" id="book-category">
                                <?php $__currentLoopData = $category; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item->id); ?>"><?php echo e($item->category); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="book-amazon-url">Amazon Url</label>
                            <input type="text " value="<?php echo e($book->book_amazon_url); ?>" name="book-amazon-url"
                                class="form-control  mt-2">
                        </div>
                    </div>
                    <?php if(!$Author): ?>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="Download">Book Media</label>
                                <div class="form-group">
                                    <a class="btn btn-primary w-100 text-left p-3" data-bs-toggle="collapse"
                                        href="#standard" role="button" aria-expanded="false" aria-controls="standard">
                                        Book Upload
                                    </a>
                                    <div class="collapse mt-2" id="standard">
                                        <div class="card p-5">
                                            <label for="">Standard</label>
                                            <small>All readers can review your book using a free copy that you provide
                                                to
                                                read.</small>
                                            <label id="bookPdf" for="bookPdfInput">
                                                <p>Click Here To Upload</p>
                                            </label>
                                            <input type="file" id="bookPdfInput" style="display: none;" name="bookPdf"
                                                class="form-control">
                                            <div id="uploadMessage" class="mt-2"></div>
                                            <?php if($errors->has('bookPdf')): ?>
                                                <span class="text-danger"><?php echo e($errors->first('bookPdf')); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <p><b>Sounds interesting!</b> We just need one file: your book cover. Find that sucker and
                                drag it in below.</p>
                            <div class="form-group">
                                <label for="">Upload Your Book Front Cover</label>
                                <input type="file" name="bookCover" class="form-control">
                                <small class="d-block mt-4"><strong><b>Accepted file types: .jpg, .png,
                                            .gif</b></strong></small>
                                <small class="d-block">Please provide the 2D image of your front cover only. We do not
                                    accept 3D models or front-spine-back covers. To grab your exact Amazon cover, you
                                    can go to your book's Amazon page, then right-click on the cover image, and click
                                    'Save image as.'</small>
                                <?php if($errors->has('bookCover')): ?>
                                    <span class="text-danger"><?php echo e($errors->first('bookCover')); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($Author): ?>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="Download">Download This Book</label>

                                <a href="<?php echo e(asset('Books/book_pdf')); ?>/<?php echo e($book->book_pdf); ?>"
                                    download="<?php echo e($book->title . '_'); ?><?php echo e($book->book_pdf); ?>"
                                    class="btn btn-primary  d-block">Download</a>
                                <small>The Book will only be Downloaded If The User Have Uploaded The Book</small>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="explicit-content">Explicit Content</label>
                            <input type="number" value="<?php echo e($book->explicit_content); ?>" max="1" min="0"
                                name="explicit-content" class="form-control mt-2">
                            <small>1 Means Yes & 0 Means No</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="explicit-content">Kindle Unlimited</label>
                            <input type="number" value="<?php echo e($book->kindle_unlimited); ?>" max="1" min="0"
                                name="kindleUnlimited" class="form-control mt-2">
                            <small>1 Means Yes & 0 Means No</small>
                        </div>
                    </div>
                    <?php if($Author): ?>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="other-information">Basic Points</label>
                                <input type="number" name="basicPoints" class="form-control mt-2"
                                    value="<?php echo e($book->BasicPoints); ?>">
                            </div>
                        </div>
                    <?php endif; ?>
<div class="col-md-6">
    <div class="form-group">
        <label for="wordCount">Please Enter The Word Count of Book</label>
        <input type="number" name="wordCount" value="<?php echo e($book->wordCount); ?>" class="form-control mt-2" required>
    </div>
</div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="book-summary">SUMMARIZE THE BOOK IN TWO OR MORE SENTENCES.</label>
                            <textarea name="book-summary" class="form-control mt-2"><?php echo e($book->book_summary); ?></textarea>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="favorite-excerpts">PLEASE COPY AND PASTE YOUR FAVORITE EXCERPT(S) FROM THE BOOK
                                BELOW.</label>
                            <textarea name="favorite-excerpts" class="form-control mt-2"><?php echo e($book->favorite_excerpts); ?></textarea>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="other-information">Short description of a book (2-3 paragraphs)</label>
                            <textarea name="other_information" class="form-control mt-2"><?php echo e($book->other_information); ?></textarea>
                        </div>
                    </div>
                    
                    
                    
                    


                    <?php if(count($quizs) == 0): ?>
                        <div class="form-group mt-5">
                            <h2>Create Quiz</h2>
                            <p>Dear Author, nobody knows your book better then you!</p>

                            <b>Here are some general tips for creating a quiz about a book:</b>
                            <p>Focus on major plot points, characters and themes: Ask questions that check if the
                                reader
                                understood the key story elements, not minor details. Things like major events,
                                character motivations and overarching themes are most important.</p>
                            <p>Avoid trick questions: The purpose is to check comprehension, not 'test' the reader.
                                Trick questions only serve to confuse or frustrate.</p>

                            <b class="text-green">Better to be light and humorous!</b>

                            <?php
                                $trueFalseQuestionsArry = range(1, $trueFalseQuestions->value);
                                $multipleChoiceQuestionsArry = range(1, $multipleChoiceQuestions->value);
                            ?>

                            <hr>

                            <h3 class="mt-3">True And False Questions</h3>
                            <?php $__currentLoopData = $trueFalseQuestionsArry; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $questions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-group mb-3">
                                    <label for="trueFalse<?php echo e($loop->index + 1); ?>"> Question <?php echo e($loop->index + 1); ?>

                                    </label>

                                    <input type="text" class="form-control rounded rounded-2 border border-1"
                                        name="trueFalse<?php echo e($loop->index + 1); ?>" id="trueFalse<?php echo e($loop->index + 1); ?>"
                                        placeholder="Enter Your Questions <?php echo e($loop->index + 1); ?>">
                                    <div>
                                        <input type="radio" name="answer<?php echo e($loop->index + 1); ?>"
                                            id="true<?php echo e($loop->index + 1); ?>" value="True"> <label
                                            for="true<?php echo e($loop->index + 1); ?>">True</label>
                                    </div>
                                    <div>
                                        <input type="radio" name="answer<?php echo e($loop->index + 1); ?>"
                                            id="False<?php echo e($loop->index + 1); ?>" value="False"> <label
                                            for="False<?php echo e($loop->index + 1); ?>">False</label>
                                    </div>
                                    <span id="trueFalseError<?php echo e($loop->index + 1); ?>"></span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <hr>

                            <h3 class="mt-3">Multiple Choice Questions</h3>
                            <?php $__currentLoopData = $multipleChoiceQuestionsArry; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $questions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-group mb-5">
                                    <label for="mcqsQuestion<?php echo e($loop->index + 1); ?>"> Question <?php echo e($loop->index + 1); ?>

                                    </label>
                                    <input type="text" class="form-control rounded rounded-2 border border-1"
                                        name="mcqsQuestion<?php echo e($loop->index + 1); ?>"
                                        id="mcqsQuestion<?php echo e($loop->index + 1); ?>"
                                        placeholder="Enter Your Questions <?php echo e($loop->index + 1); ?>">
                                    <div class="mt-2">
                                        <ol>
                                            <li class="mb-2">
                                                <div class="d-flex align-items-center border border-1 rounded rounded-2 p-0 pe-3 "
                                                    id="MCQSdiv<?php echo e($loop->index + 1); ?>1">
                                                    <input type="text" name="MCQS<?php echo e($loop->index + 1); ?>1"
                                                        class="from-control w-100 m-0 rounded rounded-2 border border-0 ps-3"
                                                        placeholder="Option 1">
                                                    <span>
                                                        <input class="m-0 h-100 w-100" type="radio"
                                                            name="rightAnswerMCQS<?php echo e($loop->index + 1); ?>" value="1">
                                                    </span>
                                                </div>
                                            </li>
                                            <li class="mb-2">
                                                <div class="d-flex align-items-center border border-1 rounded rounded-2 p-0 pe-3 "
                                                    id="MCQSdiv<?php echo e($loop->index + 1); ?>2">
                                                    <input type="text" name="MCQS<?php echo e($loop->index + 1); ?>2"
                                                        class="from-control w-100 m-0 rounded rounded-2 border border-0 ps-3"
                                                        placeholder="Option 2">
                                                    <span>
                                                        <input class="m-0 h-100 w-100" type="radio"
                                                            name="rightAnswerMCQS<?php echo e($loop->index + 1); ?>" value="2">
                                                    </span>
                                                </div>
                                            </li>
                                            <li class="mb-2">
                                                <div class="d-flex align-items-center border border-1 rounded rounded-2 p-0 pe-3 "
                                                    id="MCQSdiv<?php echo e($loop->index + 1); ?>3">
                                                    <input type="text" name="MCQS<?php echo e($loop->index + 1); ?>3"
                                                        class="from-control w-100 m-0 rounded rounded-2 border border-0 ps-3"
                                                        placeholder="Option 3">
                                                    <span>
                                                        <input class="m-0 h-100 w-100" type="radio"
                                                            name="rightAnswerMCQS<?php echo e($loop->index + 1); ?>" value="3">
                                                    </span>
                                                </div>
                                            </li>
                                            <li class="mb-2">
                                                <div class="d-flex align-items-center border border-1 rounded rounded-2 p-0 pe-3 "
                                                    id="MCQSdiv<?php echo e($loop->index + 1); ?>4">
                                                    <input type="text" name="MCQS<?php echo e($loop->index + 1); ?>4"
                                                        class="from-control w-100 m-0 rounded rounded-2 border border-0 ps-3"
                                                        placeholder="Option 4">
                                                    <span>
                                                        <input class="m-0 h-100 w-100" type="radio"
                                                            name="rightAnswerMCQS<?php echo e($loop->index + 1); ?>" value="4">
                                                    </span>
                                                </div>
                                            </li>
                                        </ol>
                                    </div>
                                    <span id="mcqError<?php echo e($loop->index + 1); ?>"></span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <h4>Book Quiz</h4>
                            <div class="col-md-12">

                                <?php $__currentLoopData = $quizs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quiz): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php $__currentLoopData = $quiz->question; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label for="Questions<?php echo e($question->id); ?>">Questions
                                            <?php echo e($loop->index + 1); ?></label>
                                        <input type="text" id="<?php echo e($question->id); ?>"
                                            name="questions-<?php echo e($question->id); ?>" class="form-control"
                                            value="<?php echo e($question->question); ?>">

                                        <?php if($question->questions_type == 'true_false'): ?>
                                            <?php $__currentLoopData = $question->answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="form-group mt-2 mb-2">
                                                    <input type="radio" <?php echo e($answer->is_correct ? 'checked' : ''); ?>

                                                        id="trueFalse-<?php echo e($answer->question_id); ?><?php echo e($loop->index); ?>"
                                                        name="trueFalse-<?php echo e($answer->question_id); ?>"
                                                        value="<?php echo e($answer->answer); ?>" />
                                                    <label
                                                        for="trueFalse-<?php echo e($answer->question_id); ?><?php echo e($loop->index); ?>"><?php echo e($answer->answer); ?></label>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>



                                        <?php if($question->questions_type == 'multiple_choice'): ?>
                                            <div class="form-group mt-2">
                                                <ol>
                                                    <?php $position = 1; ?>
                                                    <?php $__currentLoopData = $question->answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li class="mb-2">
                                                            <div class="d-flex align-items-center border border-1 rounded rounded-2 p-0 pe-3 "
                                                                id="MCQSdiv<?php echo e($position); ?>">

                                                                <input type="text"
                                                                    name="MCQS-<?php echo e($answer->id); ?>-<?php echo e($position); ?>-<?php echo e($answer->question_id); ?>"
                                                                    class="from-control w-100 m-0 rounded rounded-2 bg-white border border-0 ps-3"
                                                                    placeholder="Option <?php echo e($position); ?>"
                                                                    value="<?php echo e($answer->answer); ?>">

                                                                <span>
                                                                    <input class="m-0 h-100 w-100" type="radio"
                                                                        name="RightAnswer-<?php echo e($answer->question_id); ?>"
                                                                        <?php echo e($answer->is_correct == 1 ? 'checked' : ''); ?>

                                                                        value="<?php echo e($position); ?>" />
                                                                </span>
                                                            </div>
                                                        </li>
                                                        <?php $position++; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ol>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                    <?php endif; ?>


                    <div class="col-md-12">
                        <input value="Save" type="submit" class="btn btn-success">
                        <a href="<?php echo e(route('books.single', [$book->slug])); ?>" class="btn btn-dark">Cancel</a>
                    </div>
                </div>
                </form>
            </div>
        </section>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('additionalScript'); ?>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script>
        $(document).ready(function() {
            $('#book-category').val(<?php echo e($book->category); ?>)


            $('#bookPdfInput').on('change', function() {
                var fileName = $(this).val().split('\\').pop();

                if (fileName) {
                    $('#uploadMessage').text('File "' + fileName + '" has been uploaded successfully.');
                } else {
                    $('#uploadMessage').text('');
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/author/author-edit-single-book.blade.php ENDPATH**/ ?>