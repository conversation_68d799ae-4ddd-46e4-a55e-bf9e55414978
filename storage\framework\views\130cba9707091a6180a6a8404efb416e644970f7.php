
<?php $__env->startSection('page_title', 'Your Reviews'); ?>
<?php
    use App\Models\SystemControl;
?>
<?php $__env->startSection('page_content'); ?>
    <?php if($getActiveBooks != null): ?>
        <div class="page-content-container review-container">
            <section class="reader-hero-section bg-gray d-flex align-items-center justify-content-center reader-padding-medium pb-5">
                <div class="hero-content">
                    <div class="container">
                        <div class="row">
                            <div class="text-center reader-padding-medium reader-no-padding-bottom">
                                <?php
                                    $quote = App\Helpers\ImageHelper::getRandomQuote();
                                    $heroImage = App\Helpers\ImageHelper::getRandomImage('2.Horizontal Small');
                                ?>
                                <div class="d-flex justify-content-center reader-filter-container">
                                    <div class="reader-hero-image-container" style="max-width: 240px;">
                                        <img src="<?php echo e($heroImage['url']); ?>" alt="<?php echo e($heroImage['filename']); ?>" class="img-fluid">
                                        <div class="reader-image-caption"><?php echo e($heroImage['filename']); ?></div>
                                    </div>
                                </div>
                                <blockquote class="reader-literary-quote">
                                    <?php echo $quote; ?>

                                </blockquote>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Error messages are still shown here -->
            <?php if($errors->has('quizError')): ?>
                <div class="alert alert-danger">
                    <p><?php echo e($errors->first('quizError')); ?></p>
                </div>
            <?php endif; ?>

            <!-- Quiz results flash messages will be shown in a specific section below -->

            <section class="reader-padding-medium">
                <div class="container bg-light p-4">
                    <div class="row reader-book-container">
                        <div class="col-md-12 reader-book-card-column">
                            <div class="reader-book h-100">
                                <div class="card h-100">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="card-header">
                                                <img class="img-fluid" src="<?php echo e(asset('Books/book_cover/')); ?><?php echo e('/' . $getActiveBooks->book->front_book_cover); ?>" alt="<?php echo e($getActiveBooks->book->title); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="card-body">
                                                <div class="reader-card-top-section">
                                                    <h4 class="card-title"><?php echo e($getActiveBooks->book->title); ?></h4>
                                                    <p><?php echo e($getActiveBooks->book->book_summary); ?></p>
                                                </div>
                                                <div class="reader-card-middle-section">
                                                    <?php if($advertising->bookPrice == 'KU'): ?>
                                                        <p>Book Type: <span class="book-type">Kindle Unlimited</span></p>
                                                    <?php elseif($advertising->bookPrice > 0): ?>
                                                        <p>Book Type: <span class="book-type">Verified Purchase</span></p>
                                                        <p>$<?php echo e(number_format($advertising->bookPrice, 2)); ?></p>
                                                        <div class="alert alert-info">
                                                            <p>You must BUY this book on Amazon for your review to be stamped as Verified Purchase</p>
                                                        </div>
                                                    <?php else: ?>
                                                        <p>Book Type: <span class="book-type">Free</span></p>
                                                    <?php endif; ?>

                                                    <p>Word Count: <?php echo e(number_format($getActiveBooks->book->wordCount ?? 0)); ?></p>
                                                    <p>Turnaround Time: <?php echo e($advertising->TurnAroundTime ?? 'N/A'); ?> <?php echo e(($advertising->TurnAroundTime == 1) ? 'day' : 'days'); ?></p>
                                                    <p>Genre: <?php echo e(optional($getActiveBooks->book->categories)->category ?? 'N/A'); ?></p>

                                                    <?php
                                                        $reviews = $getActiveBooks->book->reviews;
                                                        $totalRating = 0;
                                                        $totalReviews = $reviews->count();
                                                        foreach ($reviews as $review) {
                                                            $totalRating += $review->rating;
                                                        }
                                                        $averageRating = $totalReviews > 0 ? $totalRating / $totalReviews : 0;
                                                    ?>

                                                    <div class="rating">
                                                        <p>Current Rating:
                                                            <?php if($totalReviews > 0): ?>
                                                                <?php echo e($averageRating == (int)$averageRating ? (int)$averageRating : number_format($averageRating, 1)); ?> <i class="ri-star-fill" style="color: #ffd700;"></i>
                                                                <small>(<?php echo e($totalReviews); ?> <?php echo e(\Illuminate\Support\Str::plural('review', $totalReviews)); ?>)</small>
                                                            <?php else: ?>
                                                                No Rating
                                                            <?php endif; ?>
                                                        </p>
                                                    </div>
                                                    <p>Reward Points: <?php echo e($TotalPoints - $systemPoints); ?></p>
                                                </div>
                                                <div class="reader-card-bottom-section">
                                                    <a href="<?php echo e($getActiveBooks->book->book_amazon_url); ?>" class="btn btn-text-link" id="downloadBtn">View On Amazon</a>
                                                    <a href="<?php echo e(asset('Books/book_pdf')); ?>/<?php echo e($getActiveBooks->book->book_pdf); ?>" download="<?php echo e($getActiveBooks->book->title . '_'); ?><?php echo e($getActiveBooks->book->book_pdf); ?>" class="btn btn-text-link">Download Book</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Display quiz results here if available -->
            <?php if(Session::has('success') && strpos(Session::get('success'), 'quiz-results') !== false): ?>
            <section class="reader-padding-medium quiz-results-section" id="quizResultsSection">
                <div class="container bg-light p-4">
                    <div class="quiz-header">
                        <?php echo Session::get('success'); ?>

                    </div>
                </div>
            </section>
            <?php endif; ?>

            <?php if(!$reviewExists): ?>
            <section class="reader-padding-medium timeline">
               <div class="container bg-light p-4">
                   <h3 class="page_section_heading review-section-heading">Your Timeline</h3>
                   <p class="review-text">You Have <?php echo e($displayTime); ?></p>
                   <?php if($hoursLeft <= 24 && $hoursLeft > 0): ?>
                       <div class="alert alert-success" style="border: none; border-radius: 0; background-color: #d4edda;">
                           <p class="review-text">Soon your Assignment is due. Please complete it.</p>
                       </div>
                   <?php elseif($hoursLeft <= 0): ?>
                       <?php
                           $overdueHours = abs($hoursLeft); // How many hours overdue
                           $hoursUntilCancel = $overdueCancelHours - $overdueHours; // Hours remaining until cancellation
                       ?>
                       <div class="alert alert-danger" style="border: none; border-radius: 0; background-color: #f8d7da;">
                           <?php if($hoursUntilCancel > 0): ?>
                               <p class="review-text">Your Assignment is overdue. Please complete it as soon as possible or it will be canceled in <?php echo e($hoursUntilCancel); ?> hour(s).</p>
                           <?php else: ?>
                               <p class="review-text">Your Assignment is being cancelled due to exceeding the overdue limit.</p>
                           <?php endif; ?>
                       </div>
                   <?php endif; ?>
                   <form action="<?php echo e(route('cancelAssignment', $getActiveBooks->book->id)); ?>" method="POST">
                       <?php echo csrf_field(); ?>
                       <input type="submit" value="Cancel Assignment" class="btn btn-text-link">
                   </form>
               </div>
            </section>
            <?php endif; ?>

            <?php if(!$reviewExists): ?>
            <section class="reader-padding-medium turn-in-review">
                <!-- Flash messages are now handled by master layout -->
                <div class="container bg-light p-4">
                    <div class="quiz-cont">
                        <form id="quizForm" action="<?php echo e(route('postQuizAnswer')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="book_id" value="<?php echo e($getActiveBooks->book->id); ?>">
                            <div class="alert alert-danger" id="quizError" style="display: none; background-color: #f8d7da; border: none; border-radius: 0;"></div>
                            <div class="alert alert-success" id="quizSuccess" style="display: none; background-color: #d4edda; border: none; border-radius: 0;"></div>
                            <div class="main-review-cont">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h3 class="page_section_heading review-section-heading">Please Complete This Quiz</h3>
                                        <div class="row">
<?php
   $reviewTimeLimit = $ReviewTimeLimit->value;
   $startTime = \Carbon\Carbon::parse($getActiveBooks->created_at);
   $now = \Carbon\Carbon::now();
   $hoursElapsed = $startTime->diffInHours($now);
   $remainingHours = $reviewTimeLimit - $hoursElapsed;
   if ($remainingHours < 0) {
       $remainingHours = 0;
   }
?>
                                            <h6 class="review-text">Please take your time to read the book. You need to wait at least <?php echo e($remainingHours); ?> hour(s) before you can submit a review.</h6>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 py-5">
                                                <?php
                                                    $trueFalse = 0;
                                                    $MCQs = 0;
                                                ?>
                                                <?php $__currentLoopData = $quizs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quiz): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <ol>
                                                        <?php $__currentLoopData = $quiz->question; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            
                                                            <?php if($question->questions_type == 'true_false' && $trueFalse >= $mintruefalse): ?>
                                                                <?php continue; ?>
                                                            <?php endif; ?>

                                                            
                                                            <?php if($question->questions_type == 'multiple_choice' && $MCQs >= $minimumMcqs): ?>
                                                                <?php continue; ?>
                                                            <?php endif; ?>

                                                            <li class="review-question"><?php echo e($question->question); ?></li>

                                                            <?php if($question->questions_type == 'true_false'): ?>
                                                                <?php
                                                                    $trueFalse++;
                                                                ?>
                                                                <div class="ms-2 review-radio-group">
                                                                    <?php $__currentLoopData = $question->answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <div class="form-group mt-2 mb-2">
                                                                            <label for="trueFalse-<?php echo e($answer->question_id); ?><?php echo e($loop->index); ?>">
                                                                                <input type="radio"
                                                                                    id="trueFalse-<?php echo e($answer->question_id); ?><?php echo e($loop->index); ?>"
                                                                                    name="trueFalse-<?php echo e($answer->question_id); ?>"
                                                                                    value="<?php echo e($answer->id); ?>" />
                                                                                <span class="review-answer"><?php echo e($answer->answer); ?></span>
                                                                            </label>
                                                                        </div>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </div>
                                                            <?php endif; ?>

                                                            <?php if($question->questions_type == 'multiple_choice'): ?>
                                                                <?php
                                                                    $MCQs++;
                                                                ?>
                                                                <div class="form-group mt-2 ms-2 review-radio-group">
                                                                    <?php $__currentLoopData = $question->answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <div class="form-group mb-2">
                                                                            <label for="multi_choice_question_<?php echo e($answer->question_id); ?><?php echo e($loop->index + 1); ?>">
                                                                                <input type="radio"
                                                                                    id='multi_choice_question_<?php echo e($answer->question_id); ?><?php echo e($loop->index + 1); ?>'
                                                                                    name="multi_choice_question_<?php echo e($answer->question_id); ?>"
                                                                                    value="<?php echo e($answer->id); ?>">
                                                                                <span class="review-answer"><?php echo e($answer->answer); ?></span>
                                                                            </label>
                                                                        </div>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </div>
                                                            <?php endif; ?>

                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </ol>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <input type="hidden" name="points" value='<?php echo e($TotalPoints); ?>'>
                                        <input type="hidden" name="type" value="<?php echo e($getActiveBooks->type); ?>">
                                        <input type="hidden" name="price" value="<?php echo e($advertising->bookPrice); ?>">
                                        <div class="col-md-12">
                                            <button type="submit" class="btn btn-sm btn-primary">Submit a Quiz</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </section>
        <?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quizForm = document.getElementById('quizForm');
    if (quizForm) {
        quizForm.addEventListener('submit', function(e) {
            // Get all question groups
            const trueFalseQuestions = document.querySelectorAll('[name^="trueFalse-"]');
            const multiChoiceQuestions = document.querySelectorAll('[name^="multi_choice_question_"]');

            // Create sets to track unique question names (to handle radio button groups)
            const trueFalseNames = new Set();
            const multiChoiceNames = new Set();

            // Collect all unique question names
            trueFalseQuestions.forEach(input => {
                trueFalseNames.add(input.name);
            });

            multiChoiceQuestions.forEach(input => {
                multiChoiceNames.add(input.name);
            });

            // Check if any question is unanswered
            let allAnswered = true;
            let unansweredCount = 0;

            // Check true/false questions
            trueFalseNames.forEach(name => {
                const answered = !!document.querySelector(`input[name="${name}"]:checked`);
                if (!answered) {
                    allAnswered = false;
                    unansweredCount++;
                }
            });

            // Check multiple choice questions
            multiChoiceNames.forEach(name => {
                const answered = !!document.querySelector(`input[name="${name}"]:checked`);
                if (!answered) {
                    allAnswered = false;
                    unansweredCount++;
                }
            });

            // If any questions are unanswered, prevent form submission
            if (!allAnswered) {
                e.preventDefault();
                const quizError = document.getElementById('quizError');
                const userName = "<?php echo e(Auth::user()->fullName); ?>";
                quizError.textContent = `Dear ${userName}, please Kindly Check Your Answers!`;
                quizError.innerHTML = `Dear ${userName}, please Kindly Check Your Answers!`;
                quizError.style.display = 'block';

                // Scroll to the error message
                quizError.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Hide the error message after 5 seconds
                setTimeout(function() {
                    quizError.style.display = 'none';
                }, 5000);
            }
        });
    }
});
</script>

            <?php if($reviewExists): ?>
                <section class="reader-padding-medium turn-in-review">
                    <div class="container bg-light p-4">
                        <form action="<?php echo e(route('post.review')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="quiz-cont">
                                <input type="hidden" name="totalMcqsMarks">
                            </div>
                            <div class="main-review-cont">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h3 class="page_section_heading review-section-heading">Turn In Review</h3>
                                        <div class="row">
                                            <div class="col-md-12 rating-div mb-3 review-radio-group">
                                                <input type="hidden" name="bookId"
                                                    value="<?php echo e($getActiveBooks->book->id); ?>">
                                                <small class="review-text">Your Rating:</small>
                                                <label for="rating-1">
                                                    <input type="radio" id="rating-1" name="rating" value="1">
                                                    <span class="review-answer">1 Star</span>
                                                </label>
                                                <label for="rating-2">
                                                    <input type="radio" id="rating-2" name="rating" value="2">
                                                    <span class="review-answer">2 Star</span>
                                                </label>
                                                <label for="rating-3">
                                                    <input type="radio" id="rating-3" name="rating" value="3">
                                                    <span class="review-answer">3 Star</span>
                                                </label>
                                                <label for="rating-4">
                                                    <input type="radio" id="rating-4" name="rating" value="4">
                                                    <span class="review-answer">4 Star</span>
                                                </label>
                                                <label for="rating-5">
                                                    <input type="radio" id="rating-5" name="rating" value="5">
                                                    <span class="review-answer">5 Star</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <?php if(Auth::user()->amazon_reviewer_name && !$errors->has('amazon_reviewer_name')): ?>
                                                <h6 class="review-text">Dear <strong><?php echo e(Auth::user()->fullName); ?></strong>, please check the Number Of Stars You rated This Book in Your Amazon Review under <strong><?php echo e(Auth::user()->amazon_reviewer_name); ?></strong> Reviewer Name</h6>
                                            <?php elseif(!$errors->has('amazon_reviewer_name') && !Auth::user()->amazon_reviewer_name): ?>
                                                <div class="alert alert-warning">
                                                    Dear <strong><?php echo e(Auth::user()->fullName); ?></strong>, please fill in your Amazon Reviewer Name in <a href="<?php echo e(route('profile.index')); ?>">User's Profile</a>
                                                </div>
                                            <?php endif; ?>

                                            <?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="alert alert-danger mt-2">
                                                    <?php echo e($message); ?>

                                                </div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                            <?php $__errorArgs = ['amazon_reviewer_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="alert alert-danger mt-2">
                                                    Please fill in your Amazon Reviewer Name in <a href="<?php echo e(route('profile.index')); ?>">User's Profile</a>
                                                </div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <!-- Replace hidden review field with Notice to Fellow Auteur textarea -->
                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <label for="review" class="review-text">
                                                    <strong>A Notice to a Fellow Auteur:</strong><br>
                                                    Dear Auteur, you may leave a Helpful Notice to a Book's Author with an anonymous advice on book and craft.
                                                    If appreciated, you will receive up to <?php echo e(SystemControl::where('key_type', 'notice_reward_3')->value('value')); ?> bonus Rewards!
                                                    Please Help Your Fellow Auteur with a straightforward yet truthful and candid advice on how to become even a better author!
                                                    <br><small class="text-muted">(please be kind, your notices are moderated;)</small>
                                                </label>
                                                <textarea name="review" id="review" class="form-control mt-2 review-text" rows="4" placeholder="Write your notice here..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-4">
                                        <input type="hidden" name="points" value='<?php echo e($TotalPoints); ?>'>
                                        <input type="hidden" name="type" value="<?php echo e($getActiveBooks->type); ?>">
                                        <input type="hidden" name="price" value="<?php echo e($advertising->bookPrice); ?>">
                                        <div class="col-md-12">
                                             <input type="submit" class="btn btn-sm btn-primary" value="Submit a Review">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </section>
            <?php endif; ?>

            <?php if($reviewExists): ?>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        <?php if($errors->has('rating') || $errors->has('amazon_reviewer_name')): ?>
                            // Scroll to the turn-in-review section when validation errors occur
                            document.querySelector('.turn-in-review').scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        <?php endif; ?>
                    });
                </script>
            <?php endif; ?>

            <section class="reader-padding-medium">
                <div class="container bg-light p-4">
                    <h3 class="page_section_heading review-section-heading">About This Book</h3>
                    <div class="book-meta mt-5">
                        <div>
                            <h5 class="about-heading">Favorite excerpt(s) from the book</h5>
                            <p class="about-text"><?php echo e($getActiveBooks->book->favorite_excerpts); ?></p>
                        </div>
                    </div>
                </div>
            </section>
            <script>
                // Get all elements with the class 'trimthisWord'
                var phrasesElements = document.querySelectorAll('.trimthisWord');

                // Loop through each element and modify its content
                phrasesElements.forEach(function(element) {
                    var dataFromDatabase = element.textContent;

                    // Split the data by commas and join with hyphens and line breaks
                    var dataItems = dataFromDatabase.split(',');
                    var formattedData = dataItems.map(function(item, index) {
                        var capitalizedItem = item.trim().charAt(0).toUpperCase() + item.trim().slice(1);
                        return (index + 1) + '. ' + capitalizedItem;
                    }).join('<br>');

                    // Update the content of the element
                    element.innerHTML = formattedData;

                    var currentDate = new Date();
                    var formattedDate = currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1) + '-' + currentDate
                        .getDate();
                    $('#startDate').val(formattedDate);

                    var daysToAdd = parseInt({
                        {
                            $book - > preferred_days
                        }
                    });
                    var endDate = new Date(currentDate);
                    endDate.setDate(currentDate.getDate() + daysToAdd);
                    var formattedEndDate = endDate.getFullYear() + '-' + (endDate.getMonth() + 1) + '-' +
                        endDate.getDate();
                    $('#endDate').val(formattedEndDate);
                });

                $(document).ready(function() {
                    var endDateStr = '<?php echo e($getActiveBooks->endDate); ?>';
                    console.log(endDateStr);
                    if (endDateStr) {
                        var endDate = new Date(endDateStr);
                        var today = new Date();
                        var timeDifference = endDate - today;
                        var dayDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
                        $('#days').html(dayDifference);
                    }
                });
            </script>

        </div>
    <?php else: ?>
        <div class="page-content-container review-container">
            <section class="reader-hero-section bg-gray d-flex align-items-center justify-content-center reader-padding-medium pb-5">
                <div class="hero-content">
                    <div class="container">
                        <div class="row">
                            <div class="text-center reader-padding-medium reader-no-padding-bottom">
                                <?php
                                    $quote = App\Helpers\ImageHelper::getRandomQuote();
                                    $heroImage = App\Helpers\ImageHelper::getRandomImage('2.Horizontal Small');
                                ?>
                                <div class="d-flex justify-content-center reader-filter-container">
                                    <div class="reader-hero-image-container" style="max-width: 240px;">
                                        <img src="<?php echo e($heroImage['url']); ?>" alt="<?php echo e($heroImage['filename']); ?>" class="img-fluid">
                                        <div class="reader-image-caption"><?php echo e($heroImage['filename']); ?></div>
                                    </div>
                                </div>
                                <blockquote class="reader-literary-quote">
                                    <?php echo $quote; ?>

                                </blockquote>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="reader-padding-medium">
                <div class="container bg-light p-4">
                    <h3 class="page_section_heading review-section-heading">Your Don't Have Any Active Assignments!</h3>
                </div>
            </section>
            <section class="reader-padding-medium" style="padding-bottom: 0em!important;">
                <div class="container bg-light p-4">
                    <h3 class="page_section_heading review-section-heading">Your Assignments History</h3>
                    <div class="reader-book-meta mt-5">
                        <?php $__currentLoopData = $allReviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-center gap-4 mt-3 review-history-item">
                                <div class="review-history-image">
                                    <img src="<?php echo e(asset('Books/book_cover/')); ?><?php echo e('/' . $review->book->front_book_cover); ?>"
                                        class="w-100" alt="<?php echo e($review->book->title); ?>">
                                </div>
                                <div class="review-history-content">
                                    <a href="<?php echo e(route('vault.singleBook', ['view', $review->book->slug])); ?>" class="review-history-title">
                                        <p><?php echo e($review->book->title); ?></p>
                                    </a>
                                    <p class="review-history-summary"><?php echo e($review->book->book_summary); ?></p>
                                    <p class="review-history-date">Your review of <?php echo e($review->book->title); ?> was submitted on
                                        <?php echo e(carbon\carbon::parse($review->created_at)->toDateString()); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </section>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>



<?php $__env->startSection('additionalScript'); ?>
    <script>
        $(document).ready(function() {
            // Scroll to quiz results section if it exists
            if ($("#quizResultsSection").length) {
                $("#quizResultsSection")[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
            }

            $("#downloadBtn").on("click", function(e) {
                e.preventDefault(); // Prevent the default behavior of the link

                // Copy the link to the clipboard
                var linkToCopy = $(this).attr("href");
                var tempInput = $("<input>");
                $("body").append(tempInput);
                tempInput.val(linkToCopy).select();
                document.execCommand("copy");
                tempInput.remove();

                // Show the alert
                alert("Link copied to clipboard: " + linkToCopy);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/dashboard/review.blade.php ENDPATH**/ ?>