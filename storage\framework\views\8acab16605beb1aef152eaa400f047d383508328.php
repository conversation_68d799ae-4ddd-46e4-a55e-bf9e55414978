
<?php $__env->startSection('page_title', 'Register'); ?>
<?php $__env->startSection('page_content'); ?>
    <!-- Flash messages are now handled by master layout -->
    <div class="container registeration-page padding-medium">
        <div class="row d-flex justify-content-center align-items-center content-container">
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-12">
                        <img src="<?php echo e(asset('image/checkout-unlimited.png')); ?>" width="300" class="img-fluid" alt="">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <?php
                            $currentFlow = \App\Services\SubscriptionFlowService::getCurrentFlow();
                            $isAlphaFlow = $currentFlow === 'alpha';
                        ?>

                        <?php if(isset($plan) && $plan == 'auteur_plus'): ?>
                            <div class="pricing-box featured">
                                <div class="pricing-title">
                                    <h2><span class="highlighted-title">AUTEUR+</span></h2>
                                    <div class="price">
                                        <p><b>$<?php echo e($currentFlow === 'production' ? '240 yearly ($10 per month)' : '13/month'); ?></b></p>
                                        <?php if($currentFlow === 'production'): ?>
                                            <p><span class="badge bg-info text-white">Free for 10 days</span></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="pricing-features">
                                    <ul>
                                        <li>Add unlimited books</li>
                                        <li>Get up to 7 reviews per week</li>
                                        <li>Advanced analytics</li>
                                        <li>Priority support</li>
                                    </ul>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="pricing-box">
                                <div class="pricing-title">
                                    <h2><span class="highlighted-title"><?php echo e($currentFlow === 'alpha' ? 'AUTEUR Alpha' : 'AUTEUR'); ?></span></h2>
                                    <div class="price">
                                        <?php if($currentFlow === 'production'): ?>
                                            <p><b>$15 per month</b></p>
                                            <p><span class="badge bg-info text-white">Free for 10 days</span></p>
                                        <?php else: ?>
                                            <p><b>FREE in <?php echo e($currentFlow === 'alpha' ? 'ALPHA' : 'BETA'); ?></b> <u>No Credit Card Required!</u></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="pricing-features">
                                    <ul>
                                        <li>Add up to <?php echo e($currentFlow === 'production' ? '3' : '3'); ?> books</li>
                                        <li>Get up to <?php echo e($currentFlow === 'production' ? '5' : '3'); ?> reviews per week</li>
                                        <li>Basic analytics</li>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <hr>
                <div class="row">
    <div class="col-md-12">
        <div class="pricing-box">
            <div class="pricing-title">
                <h2><span class="highlighted-title">Try Risk-Free</span></h2>
            </div>
            <div class="pricing-features">
                <ul>
                    <li><i class="ri-check-double-line"></i>Unlimited Reviews for Each Book</li>
                    <li><i class="ri-check-double-line"></i>No Charge Per Review</li>
                    <li><i class="ri-check-double-line"></i>Cancel at Anytime</li>
                </ul>
            </div>
        </div>
    </div>
</div>
            </div>
            <div class="col-md-6">
                <!-- Google Registration Option -->
                <div class="text-center mb-4">
                    <h4 class="mb-3">Register with</h4>
                    <a href="<?php echo e(route('register.google')); ?>" class="btn btn-outline-dark w-100 py-2">
                        <i class="ri-google-fill me-2"></i>Continue with Google
                    </a>
                </div>

                <div class="text-center mb-4">
                    <span class="divider-text">or register with Email</span>
                </div>

                <!-- Email Registration Form -->
                <form action="<?php echo e(route('registerUser')); ?>" method="POST" class="form ">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="plan" value="<?php echo e($plan ?? 'auteur'); ?>">

                    <div class="row mt-3">
                        <div class="col-md-12">
                           <div class="form-group">
                                <label for="" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" value="<?php echo e(old('fullName')); ?>" name="fullName" required class="form-control">
                                <?php if($errors->has('fullName')): ?>
                                    <span class="text-danger"><?php echo e($errors->first('fullName')); ?></span>
                                <?php endif; ?>
                           </div>
                        </div>
                    </div>


                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="" class="form-label">Email <span class="text-danger">*</span></label>
                                <input value="<?php echo e(old('email')); ?>" type="email" name="email" required class="form-control">
                            </div>
                            <?php if($errors->has('email')): ?>
                                    <span class="text-danger"><?php echo e($errors->first('email')); ?></span>
                                <?php endif; ?>
                        </div>
                    </div>


                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="from-group">
                                <label for="" class="form-label">Password <span class="text-danger">*</span></label>
                                <input value="<?php echo e(old('password')); ?>" type="password" name="password" required  class="form-control">
                            </div>
                            <?php if($errors->has('password')): ?>
                                    <span class="text-danger"><?php echo e($errors->first('password')); ?></span>
                                <?php endif; ?>
                        </div>
                    </div>

                    <!-- Hidden Postal Code Field -->
                    <input type="hidden" name="zip" value="00000">
<div class="pricing-features  mt-2">
    <p>By clicking below, you agree to our <a href="<?php echo e(route('terms')); ?>">Terms of Service</a>.</p>
</div>

<div class="row d-flex justify-content-start align-items-center mt-n3">
    <button type="submit" class="btn btn-primary w-50">Register</button>
</div>
                </form>
            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/auth/register.blade.php ENDPATH**/ ?>