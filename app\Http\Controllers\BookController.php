<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\AdvertisingQueue;
use App\Models\Book;
use App\Models\Category;
use App\Models\QuizAnswers;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Advertising;
use App\Models\BookQuiz;
use App\Models\Quiz;
use App\Models\QuizQuestions;
use App\Models\Reader;
use App\Models\Review;
use App\Models\SystemControl;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Client;

class BookController extends Controller
{
    public function viewCreateBook()
    {
        $categories = Category::get();
        $authorTrueFalse = SystemControl::where('key_type', 'TrueFalseQuetions')->first();
        $authorMultipleChoice = SystemControl::where('key_type', 'multipleChoiceQuestions')->first();
        return view('book.create-book', compact('categories', 'authorMultipleChoice', 'authorTrueFalse'));
    }
    public function createBook(Request $req)
    {
        if ($req->hasFile('bookPdf')) {
            $extension = strtolower($req->bookPdf->getClientOriginalExtension());
            if ($extension != 'pdf' && $extension != 'epub') {
                return redirect()->back()->withErrors(['bookPdf' => 'Only PDF and EPUB files are allowed'])->withInput();
            }
        }

        $req->validate([
            'bookTitle' => 'required',
            'bookAuthor' => 'required',
            'bookCover' => 'required|mimes:jpeg,jpg,png,webp',
            'bookPdf' => 'required',
            'bookUrl' => 'required',
            'bookGenre' => [
                'required',
                'exists:categories,id',
            ],
            'bookSummary' => 'required|max:99',
            'wordCount' => 'required',
            'other_information' => 'required|min:100|max:1000',
            'favorite_excerpts' => 'required|min:1000|max:2000',
        ]);

        $newBook = new Book;

        $pdfName = time() . '.' . $req->bookPdf->extension();
        $req->bookPdf->move(public_path('Books/book_pdf'), $pdfName);

        $coverimgName = time() . '.' . $req->bookCover->extension();
        $req->bookCover->move(public_path('Books/book_cover'), $coverimgName);

        $newBook->title = $req->bookTitle;
        $newBook->author = $req->bookAuthor;
        $newBook->book_pdf = $pdfName;
        $newBook->category = $req->bookGenre;
        $newBook->front_book_cover = $coverimgName;
        $newBook->book_amazon_url = $req->bookUrl;
        if ($req->explicitContent) {
            $newBook->explicit_content = $req->explicitContent;
        }
        $newBook->book_summary = $req->bookSummary;
        $newBook->slug = Str::slug($newBook->title);
        $newBook->wordCount = $req->wordCount;
        $newBook->publish_by = Auth::user()->id;
        $newBook->favorite_excerpts = $req->favorite_excerpts;
        $newBook->other_information = $req->other_information;

        $newBook->save();

        $activity = new Activity;
        $activity->bookId = $newBook->id;
        $activity->reason = "You have submitted a book for Approval";
        $activity->notification_type = 'author';
        $activity->save();

        // True And False For Loop
        $trueFalse = [];
        $trueFalseQuestions = SystemControl::where('key_type', 'TrueFalseQuetions')->first();

        if ($trueFalseQuestions) {
            for ($i = 1; $i <= $trueFalseQuestions->value; $i++) {
                $questionKey = 'trueFalse' . $i;
                $answerKey = 'answer' . $i;

                if ($req->has($questionKey) && $req->has($answerKey)) {
                    $trueFalse[] = [
                        'question' => $req->$questionKey,
                        'answer' => $req->$answerKey
                    ];
                }
            }
        }

        // MCQs For Loop
        $MCSQs = [];
        $multipleChoiceQuestions = SystemControl::where('key_type', 'multipleChoiceQuestions')->first();

        if ($multipleChoiceQuestions) {
            // Server-side validation for MCQ questions: Ensure that if a question is provided, a correct answer must be selected
            for ($i = 1; $i <= $multipleChoiceQuestions->value; $i++) {
                $questionKey = 'mcqsQuestion' . $i;
                $rightAnswerKey = 'rightAnswerMCQS' . $i;
                if ($req->filled($questionKey) && !$req->filled($rightAnswerKey)) {
                    return back()->withErrors([$rightAnswerKey => "Please select the correct answer for question $i"])->withInput();
                }
            }

            for ($i = 1; $i <= $multipleChoiceQuestions->value; $i++) {
                $questionKey = 'mcqsQuestion' . $i;
                $answer1Key = 'MCQS' . $i . '1';
                $answer2Key = 'MCQS' . $i . '2';
                $answer3Key = 'MCQS' . $i . '3';
                $answer4Key = 'MCQS' . $i . '4';
                $rightAnswerKey = 'rightAnswerMCQS' . $i;

                if ($req->has($questionKey) && $req->has($answer1Key) && $req->has($answer2Key) && $req->has($answer3Key) && $req->has($answer4Key) && $req->has($rightAnswerKey)) {
                    $MCSQs[] = [
                        'question' => $req->$questionKey,
                        'choices' => [
                            'choice1' => $req->$answer1Key,
                            'choice2' => $req->$answer2Key,
                            'choice3' => $req->$answer3Key,
                            'choice4' => $req->$answer4Key
                        ],
                        'answer' => intval($req->$rightAnswerKey)
                    ];
                }
            }
        }

        $quiz = BookQuiz::insert([
            'book_id' => $newBook->id
        ]);

        $quiz = BookQuiz::where('book_id', $newBook->id)->first();

        foreach ($trueFalse as $item) {
            $quizQuestion = new QuizQuestions;
            $quizQuestion->quiz_id = $quiz->id;
            $quizQuestion->question = $item['question'];
            $quizQuestion->questions_type = 'true_false';
            $quizQuestion->save();

            $quizAnswer = new QuizAnswers;
            $quizAnswer->question_id = $quizQuestion->id;
            $quizAnswer->answer = $item['answer'];
            $quizAnswer->is_correct = 1;
            $quizAnswer->save();

            $incorrectAnswer = new QuizAnswers;
            $incorrectAnswer->question_id = $quizQuestion->id;
            $incorrectAnswer->answer = ($item['answer'] == 'True') ? 'False' : 'True';
            $incorrectAnswer->is_correct = 0;
            $incorrectAnswer->save();
        }

        foreach ($MCSQs as $mcq) {
            $quizQuestion = new QuizQuestions;
            $quizQuestion->quiz_id = $quiz->id;
            $quizQuestion->question = $mcq['question'];
            $quizQuestion->questions_type = 'multiple_choice';
            $quizQuestion->save();

            $count = 0;
            foreach ($mcq['choices'] as $index => $choice) {
                if (!empty($choice)) {

                $count++;
                $quizAnswer = new QuizAnswers;
                $quizAnswer->question_id = $quizQuestion->id;
                $quizAnswer->answer = $choice;
                $quizAnswer->is_correct = $count === $mcq['answer'] ? 1 : 0;
                }

                $quizAnswer->save();
            }
        }

        return redirect()->route('author')->with('success', 'Your book has been submitted for approval. Admin will review it shortly.');
    }
    public function showSingleBook($slug)
    {
        $turnArounodTime = SystemControl::where('key_type', 'TurnAroundTime')->get();
        $virtualPoints = SystemControl::where('key_type', 'VP')->get();
        $KindlePoints = SystemControl::where('key_type', 'KU')->first();

        // Get reward values for notices
        $r0 = SystemControl::where('key_type', 'notice_fee')->first() ? SystemControl::where('key_type', 'notice_fee')->first()->value : 2;
        $r1 = SystemControl::where('key_type', 'notice_reward_1')->first() ? SystemControl::where('key_type', 'notice_reward_1')->first()->value : 5;
        $r2 = SystemControl::where('key_type', 'notice_reward_2')->first() ? SystemControl::where('key_type', 'notice_reward_2')->first()->value : 10;
        $r3 = SystemControl::where('key_type', 'notice_reward_3')->first() ? SystemControl::where('key_type', 'notice_reward_3')->first()->value : 15;

        $book = Book::where('slug', $slug)->first();
        $activity = Activity::where('bookId', $book->id)
                    ->where('notification_type', 'author')  // Only show author notifications
                    ->orderBy('created_at', 'desc')  // Most recent first
                    ->get();

        // Get all reviews for this book
        $allReviews = Review::where('bookId', $book->id)
                    ->orderBy('created_at', 'desc')  // Most recent first
                    ->get();

        // Filter out rejected reviews (status 3)
        $reviews = $allReviews->filter(function($review) {
            return $review->reviewStatus != 3;
        });

        $countBookInAdvertising = Advertising::where('bookId', $book->id)->where('reeader_required', '>=', '0')->count();
        $countBookInQueue = AdvertisingQueue::where('bookId', $book->id)->count();

        $totalBookReadersRequired = $countBookInAdvertising + $countBookInQueue;

        $countReaders = Reader::where('bookId', $book->id)->where('status', '1')->count();

        if (!$book) {
            abort(404);
        } else {
            $advert = Advertising::where('bookId', $book->id)->get();
            $data = json_decode($advert);
        }

        $totalRating = 0;
        $totalReviews = $reviews->count();
        foreach ($reviews as $review) {
            $totalRating += $review->rating;
        }

        $averageRating = $totalReviews > 0 ? $totalRating / $totalReviews : 0;
        return view('book.view-single-book-author', compact('book', 'data', 'activity', 'reviews', 'averageRating', 'totalBookReadersRequired', 'countReaders', 'turnArounodTime', 'virtualPoints', 'KindlePoints', 'r0', 'r1', 'r2', 'r3'));
    }

    public function approve(Request $request, $id)
    {
        // Validate the request if necessary
        // Update the book's status to 1 in the database
        $book = Book::where('id', $id)->first();
        if ($book) {
            $book->approval_status = 1;
            $book->save();

            // Create activity for book approval
            $activity = new Activity;
            $activity->bookId = $id;
            $activity->userid = $book->publish_by; // Set the user ID to the book's author
            $activity->reason = "Your book has been approved";
            $activity->notification_type = 'author';
            $activity->save();
        }

        // Redirect back to the book list or wherever you want
        return redirect()->back()->with('success', 'Book approved successfully');
    }
}
