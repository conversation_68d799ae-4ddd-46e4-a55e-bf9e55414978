<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/tests/Unit" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth1-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php81" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>