
<?php $__env->startSection('page_title', 'Login'); ?>
<?php $__env->startSection('page_content'); ?>
    <div class="container" style="margin-top: 10vh;">
        <div class="row">
            <!-- Flash messages are now handled by master layout -->
            <div class="col-md-12">
                <?php if($errors->any()): ?>
                    <!-- Validation errors are still displayed here -->
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="alert alert-danger"><?php echo e($error); ?></div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
            <div class="col-md-3"></div>
            <div class="col-md-6">
                <!-- Google Login Option -->
                <div class="text-center mb-4">
                    <h4 class="mb-3">Login with</h4>
                    <a href="<?php echo e(route('login.google')); ?>" class="btn btn-outline-dark w-100 py-2">
                        <i class="ri-google-fill me-2"></i>Continue with Google
                    </a>
                </div>

                <div class="text-center mb-4">
                    <span class="divider-text">or login with Email</span>
                </div>

                <!-- Email Login Form -->
                <form action="<?php echo e(route('loginUser')); ?>" method="POST" class="form login-form">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="Email" class="form-label">Email</label>
                                <input type="email" name="email" placeholder="<EMAIL>" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="Email" class="form-label">Password</label>
                                <input type="password" name="password" placeholder="****************" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row d-flex justify-content-center align-items-center mt-5">
                        <button type="submit" class="btn btn-primary w-50">Login</button>
                    </div>
                </form>
            </div>
            <div class="col-md-3"></div>
        </div>
    </div>



    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user was redirected due to session expiration
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('session_expired') === 'true') {
                // Show a more prominent message for session expiration
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <i class="ri-time-line me-2"></i>
                    <strong>Session Expired:</strong> Your session has expired due to inactivity. Please log in again to continue.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // Insert the alert at the top of the form container
                const container = document.querySelector('.col-md-6');
                container.insertBefore(alertDiv, container.firstChild);

                // Auto-focus on email field
                const emailField = document.querySelector('input[name="email"]');
                if (emailField) {
                    emailField.focus();
                }
            }
        });
    </script>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('master-layout.master-layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/auth/login.blade.php ENDPATH**/ ?>